package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"gooze-vben-api/internal/logic"
	"gooze-vben-api/models"

	"github.com/gin-gonic/gin"
	"github.com/soryetong/gooze-starter/gooze"
	"github.com/soryetong/gooze-starter/pkg/gzauth"
	"go.uber.org/zap"
)

// responseWriter 包装 gin.ResponseWriter 以捕获响应内容
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// RecordMiddleware 操作日志记录中间件
func RecordMiddleware() gin.HandlerFunc {
	systemLogic := logic.NewSystemLogic()

	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			// 重新设置请求体，以便后续处理器可以读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器以捕获响应内容
		responseBody := &bytes.Buffer{}
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:           responseBody,
		}
		c.Writer = writer

		// 继续处理请求
		c.Next()

		// 计算耗时
		elapsed := time.Since(startTime)

		// 获取用户信息
		var username string
		var userId int64
		if userIdValue := gzauth.GetTokenValue[int64](c, "id"); userIdValue != 0 {
			userId = userIdValue
			username = gzauth.GetTokenValue[string](c, "username")
		}

		// 获取API描述
		path := strings.TrimPrefix(c.Request.URL.Path, "/api/v1")
		description := systemLogic.GetRecordDescription(path, c.Request.Method)

		// 获取响应内容
		var responseStr string
		var msg string
		if responseBody.Len() > 0 {
			responseStr = responseBody.String()
			// 尝试解析响应获取msg
			var respData map[string]any
			if err := json.Unmarshal(responseBody.Bytes(), &respData); err == nil {
				if msgValue, ok := respData["msg"]; ok {
					msg = fmt.Sprintf("%v", msgValue)
				}
			}
		}

		// 限制请求和响应内容长度
		requestStr := string(requestBody)
		if len(requestStr) > 2000 {
			requestStr = requestStr[:2000] + "..."
		}
		if len(responseStr) > 2000 {
			responseStr = responseStr[:2000] + "..."
		}

		// 创建操作日志记录
		record := &models.SysRecords{
			Username:    username,
			UserId:      userId,
			Description: description,
			Method:      c.Request.Method,
			Path:        path,
			StatusCode:  int64(c.Writer.Status()),
			Elapsed:     elapsed.String(),
			Msg:         msg,
			Request:     requestStr,
			Response:    responseStr,
			Platform:    c.GetHeader("User-Agent"),
			Ip:          c.ClientIP(),
			Address:     "", // 可以根据IP获取地址信息
		}

		// 异步保存日志记录，避免影响响应性能
		go func() {
			if err := gooze.Gorm().Create(record).Error; err != nil {
				gooze.Log.Error("保存操作日志失败", zap.Error(err))
			}
		}()
	}
}
